package com.ideal.envc.compare;

import com.alibaba.fastjson2.JSON;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.ChangeDelta;
import com.github.difflib.patch.Chunk;
import com.github.difflib.patch.DeleteDelta;
import com.github.difflib.patch.InsertDelta;
import com.github.difflib.patch.Patch;
import com.github.difflib.text.DiffRow;
import com.github.difflib.text.DiffRowGenerator;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CompareSourceTargetContentManager升级版本
 * 基于java-diff-utils 4.12版本实现
 * 保持与原版完全相同的输入输出接口和业务逻辑
 *
 * 采用单例模式设计，线程安全，提供高性能的文本比对功能
 *
 * <AUTHOR>
 */
public class CompareSourceTargetContentManagerV2 {

    /**
     * 单例实例（饿汉式，线程安全）
     */
    private static final CompareSourceTargetContentManagerV2 INSTANCE = new CompareSourceTargetContentManagerV2();

    /**
     * 私有构造函数，防止外部实例化
     */
    private CompareSourceTargetContentManagerV2() {
        // 私有构造函数
    }

    /**
     * 获取单例实例
     *
     * @return CompareSourceTargetContentManagerV2单例实例
     */
    public static CompareSourceTargetContentManagerV2 getInstance() {
        return INSTANCE;
    }
    // 格式验证和文件名提取的正则表达式（与原版保持一致）
    private static final Pattern FORMAT_PATTERN = Pattern.compile(
        "^.+\\s+\\(size:\\s+[\\d.]+\\s*[KMGT]?B?" +
        "(?:,\\s*permissions:\\s+[rwx-]+,\\s*MD5:\\s+[a-fA-F0-9]+)?\\)$"
    );
    private static final Pattern FILENAME_PATTERN = Pattern.compile("^(.*?)\\s+\\(size: ");

    // 结果常量（与原版保持一致）
    public static final String RESULTEQUAL = "EQUAL";
    public static final String RESULTCHANGE = "CHANGE";
    public static final String RESULTINSERT = "INSERT";
    public static final String RESULTDELETE = "DELETE";
    public static final String COMPARERESULT = "compareResult";
    public static final String RET = "ret";
    public static final String SOURCECONTENT = "sourceContent";
    public static final String TARGETCONTENT = "targetContent";
    public static final String WORKFLOWID = "workFlowId";
    public static final String SOURCETYPE = "sourceActType";
    public static final String TARGETTYPE = "targetActType";
    public static final String CALLFLOW = "CallFlow";

    // HTML模板常量（修复版本 - 与原版完全一致的HTML结构）
    private static final String HTML_TR_SPACER = "<tr height=\"5\"><td></td><td width=\"5\"></td><td class=\"cp_line\"></td><td width=\"5\"></td><td></td></tr><tr>";
    private static final String HTML_TR_SPACER_CLASS = "<tr height=\"5\" class=\"cps_tr1\"><td></td><td width=\"5\"></td><td class=\"cp_line\"></td><td width=\"5\"></td><td></td></tr>";
    private static final String HTML_TABLE_START = "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr>";
    private static final String HTML_DIV_SEPARATOR = "</div></td><td><div class=\"cp_cn\">";
    private static final String HTML_TABLE_END_MIDDLE = "</div></td></tr></table></td><td width=\"5\"></td><td class=\"cp_line\"></td>";
    private static final String HTML_PLACEHOLDER_CELL = "<td><div class=\"placeholder\"></div></td><td><div class=\"cp_text\">";
    private static final String HTML_ROW_END = "</div></td></tr></table></td></tr>";

    // 预构建的HTML片段
    private static final String HTML_DELETE_FRAME_START = "<td class=\"cp_frame abnormal cpi_td_w\">";
    private static final String HTML_INSERT_FRAME_START = "<td class=\"cp_frame cpi_td_w\">";
    private static final String HTML_INSERT_FRAME_COMPLETE_START = "<td class=\"cp_frame complete cpi_td_w\">";
    private static final String HTML_CHANGE_FRAME_START = "<td class=\"cp_frame warning cpi_td_w\">";
    private static final String HTML_ICON_POS = "<td><div class=\"cp_icon icon_pos\"></div></td><td><div class=\"cp_text\">";
    private static final String HTML_ICON_POS2 = "<td><div class=\"cp_icon icon_pos2\"></div></td><td><div class=\"cp_text\">";
    private static final String HTML_ICON_POS3 = "<td><div class=\"cp_icon icon_pos3\"></div></td><td><div class=\"cp_text\">";
    private static final String HTML_WIDTH_5_TD = "<td width=\"5\"></td>";

    /**
     * 字符串比对方法（上层封装方法）
     * 接收两个字符串，按换行符分割后调用核心比对方法
     *
     * @param sourceContent 源内容字符串
     * @param targetContent 目标内容字符串
     * @return 比对结果Map，包含compareResult(HTML)和ret(boolean)
     */
    public Map<String, Object> compare(String sourceContent, String targetContent) {
        // 处理null值，避免空指针异常
        if (sourceContent == null) {
            sourceContent = "";
        }
        if (targetContent == null) {
            targetContent = "";
        }

        // 按换行符分割字符串为行列表
        // 使用split方法处理不同操作系统的换行符（\n, \r\n, \r）
        List<String> sourceLines = splitToLines(sourceContent);
        List<String> targetLines = splitToLines(targetContent);

        // 调用核心比对方法
        return compare(sourceLines, targetLines);
    }

    /**
     * 将字符串按换行符分割为行列表
     * 支持不同操作系统的换行符格式
     *
     * @param content 待分割的字符串内容
     * @return 分割后的行列表
     */
    private List<String> splitToLines(String content) {
        if (content == null || content.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用正则表达式处理不同类型的换行符
        // \r\n (Windows), \n (Unix/Linux), \r (Mac)
        String[] lines = content.split("\\r?\\n|\\r");

        // 转换为可变列表
        List<String> lineList = new ArrayList<>(Arrays.asList(lines));

        // 处理特殊情况：如果原字符串以换行符结尾，split会忽略最后的空字符串
        // 需要根据实际需求决定是否保留这个空行
        if (content.endsWith("\n") || content.endsWith("\r\n") || content.endsWith("\r")) {
            // 如果原字符串以换行符结尾，添加一个空行以保持一致性
            lineList.add("");
        }

        return lineList;
    }

    /**
     * 核心比对方法
     * 与原版CompareSourceTargetContentManager.compare()方法保持完全相同的接口和输出格式
     *
     * @param aLines 源文件行列表
     * @param bLines 目标文件行列表
     * @return 比对结果Map，包含compareResult(HTML)和ret(boolean)
     */
    public Map<String, Object> compare(List<String> aLines, List<String> bLines) {
        Map<String, Object> outputs = new HashMap<>();
        StringBuilder result = new StringBuilder();
        StringBuilder compareResult = new StringBuilder();
        
        // 生成HTML表格头部（与原版完全一致）
        compareResult.append("<div class=\"comparison_space\">"
                + "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" class=\"comparison_tab\">"
                + "<tr class=\"cp_title\"><td><span>Source</span></td><td width=\"5\"></td>"
                + "<td class=\"cp_line\"></td><td width=\"5\"></td><td><span>Target</span></td></tr>");
        
        boolean ret = true;
        
        // 行数限制检查（与原版保持一致）
        if (aLines.size() > 10000) {
            result.append("The number of lines in the source file exceeds 10,000!!!");
            outputs.put(COMPARERESULT, result.toString());
            outputs.put(RET, false);
            return outputs;
        }
        if (bLines.size() > 10000) {
            result.append("The number of lines in the target file exceeds 10,000!!!");
            outputs.put(COMPARERESULT, result.toString());
            outputs.put(RET, false);
            return outputs;
        }

        // 使用新版本API创建DiffRowGenerator
        DiffRowGenerator.Builder builder = DiffRowGenerator.create();
        DiffRowGenerator dfg = builder.build();

        // 根据格式生成不同的Patch（保持原版逻辑）
        Patch<String> patch;
        boolean isFormatValid = isAllLinesValidFormat(aLines) && isAllLinesValidFormat(bLines);
        if (isFormatValid) {
            // 符合格式：使用自定义Patch（按文件名比对）
            patch = generateCustomFileNamePatch(aLines, bLines);
        } else {
            // 不符合格式：使用默认Patch
            patch = DiffUtils.diff(aLines, bLines);
        }
        
        // 生成差异行
        // 注意：虽然只传入了aLines，但bLines的内容已经包含在patch的target chunks中
        List<DiffRow> rows = dfg.generateDiffRows(aLines, patch);
        int rowsSize = rows.size();
        int insertSize = 0;
        int deleteSize = 0;
        int oldSize = aLines.size();
        int newSize = bLines.size();
        
        // 处理每一行差异（保持原版逻辑）
        for (int i = 0; i < rowsSize; i++) {
            DiffRow diffRow = rows.get(i);
            // 确保tag字符串与原版本一致
            String tag = convertTagToString(diffRow.getTag());
            DiffRow.Tag tag1 = diffRow.getTag();
            String oldLine = diffRow.getOldLine();
            String newLine = diffRow.getNewLine();
            oldLine = getReplace(oldLine);
            newLine = getReplace(newLine);
            String oldNum = String.valueOf(i + 1 - insertSize);
            String newNum = String.valueOf(i + 1 - deleteSize);
            
            // 行号计算逻辑（与原版完全一致）
            if ((i + 1 - insertSize) > oldSize) {
                ret = false;
                tag1 = DiffRow.Tag.INSERT;
                tag = RESULTINSERT;
                insertSize++;
                oldNum = "";
                newNum = String.valueOf(i + 1 - deleteSize);
            } else if ((i + 1 - deleteSize) > newSize) {
                ret = false;
                tag1 = DiffRow.Tag.DELETE;
                tag = RESULTDELETE;
                deleteSize++;
                oldNum = String.valueOf(i + 1 - insertSize);
                newNum = "";
            } else if (RESULTCHANGE.equals(tag)) {
                ret = false;
                if ((i + 1 - insertSize) > oldSize || (oldLine != null && oldLine.length() == 0
                        && !oldLine.equals(aLines.get(i - insertSize)))) {
                    tag1 = DiffRow.Tag.INSERT;
                    tag = RESULTINSERT;
                    insertSize++;
                    oldNum = "";
                    newNum = String.valueOf(i + 1 - deleteSize);
                } else if ((i + 1 - deleteSize) > newSize
                        || newLine != null && newLine.length() == 0 && !newLine.equals(bLines.get(i - deleteSize))) {
                    tag1 = DiffRow.Tag.DELETE;
                    tag = RESULTDELETE;
                    deleteSize++;
                    oldNum = String.valueOf(i + 1 - insertSize);
                    newNum = "";
                }
            } else if (RESULTINSERT.equals(tag)) {
                insertSize++;
                ret = false;
                oldNum = "";
                newNum = String.valueOf(i + 1 - deleteSize);
            } else if (RESULTDELETE.equals(tag)) {
                deleteSize++;
                ret = false;
                oldNum = String.valueOf(i + 1 - insertSize);
                newNum = "";
            }
            diffRow.setTag(tag1);
            initResultStr(tag, compareResult, oldNum, oldLine, newNum, newLine);
        }
        
        compareResult.append("</table></div>");
        outputs.put(COMPARERESULT, compareResult.toString());
        outputs.put(RET, ret);
        return outputs;
    }

    // 格式验证方法（与原版保持一致）
    private boolean isAllLinesValidFormat(List<String> lines) {
        if (lines == null || lines.isEmpty()) {
            return true;
        }
        for (String line : lines) {
            if (line == null || !FORMAT_PATTERN.matcher(line).matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * HTML结果字符串生成方法（修复版本 - 与原版完全一致的HTML结构）
     * 修复了HTML结构错误，确保生成的HTML与原版完全一致
     */
    public void initResultStr(String tag, StringBuilder compareResult, String oldNum, String oldLine, String newNum,
                              String newLine) {
        // 预估容量以减少StringBuilder扩容次数（基于实际HTML长度估算）
        int estimatedCapacity = 500 +
            (oldNum != null ? oldNum.length() : 0) +
            (oldLine != null ? oldLine.length() : 0) +
            (newNum != null ? newNum.length() : 0) +
            (newLine != null ? newLine.length() : 0);

        // 确保StringBuilder有足够容量
        compareResult.ensureCapacity(compareResult.length() + estimatedCapacity);

        if (RESULTDELETE.equals(tag)) {
            // DELETE分支：对应原版 str1 + "<td class=\"cp_frame abnormal cpi_td_w\">" + str2 + "<td><div class=\"cp_icon icon_pos\"></div></td><td><div class=\"cp_text\">" + oldNum + str3 + oldLine + str4 + "<td width=\"5\"></td><td class=\"cp_frame cpi_td_w\">" + str2 + str5 + newNum + str3 + newLine
            compareResult.append(HTML_TR_SPACER)
                    .append(HTML_DELETE_FRAME_START)
                    .append(HTML_TABLE_START)
                    .append(HTML_ICON_POS)
                    .append(oldNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(oldLine)
                    .append(HTML_TABLE_END_MIDDLE)
                    .append(HTML_WIDTH_5_TD)
                    .append(HTML_INSERT_FRAME_START)
                    .append(HTML_TABLE_START)
                    .append(HTML_PLACEHOLDER_CELL)
                    .append(newNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(newLine);
        } else if (RESULTINSERT.equals(tag)) {
            // INSERT分支：对应原版 str1 + "<td class=\"cp_frame cpi_td_w\">" + str2 + str5 + oldNum + str3 + oldLine + str4 + "<td width=\"5\"></td><td class=\"cp_frame complete cpi_td_w\">" + str2 + "<td><div class=\"cp_icon icon_pos2\"></div></td><td><div class=\"cp_text\">" + newNum + str3 + newLine
            compareResult.append(HTML_TR_SPACER)
                    .append(HTML_INSERT_FRAME_START)
                    .append(HTML_TABLE_START)
                    .append(HTML_PLACEHOLDER_CELL)
                    .append(oldNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(oldLine)
                    .append(HTML_TABLE_END_MIDDLE)
                    .append(HTML_WIDTH_5_TD)
                    .append(HTML_INSERT_FRAME_COMPLETE_START)
                    .append(HTML_TABLE_START)
                    .append(HTML_ICON_POS2)
                    .append(newNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(newLine);
        } else if (RESULTCHANGE.equals(tag)) {
            // CHANGE分支：对应原版 str1 + "<td class=\"cp_frame warning cpi_td_w\">" + str2 + "<td><div class=\"cp_icon icon_pos3\"></div></td><td><div class=\"cp_text\">" + oldNum + str3 + oldLine + str4 + "<td width=\"5\"></td><td class=\"cp_frame warning cpi_td_w\">" + str2 + "<td><div class=\"cp_icon icon_pos3\"></div></td><td><div class=\"cp_text\">" + newNum + str3 + newLine
            compareResult.append(HTML_TR_SPACER)
                    .append(HTML_CHANGE_FRAME_START)
                    .append(HTML_TABLE_START)
                    .append(HTML_ICON_POS3)
                    .append(oldNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(oldLine)
                    .append(HTML_TABLE_END_MIDDLE)
                    .append(HTML_WIDTH_5_TD)
                    .append(HTML_CHANGE_FRAME_START)
                    .append(HTML_TABLE_START)
                    .append(HTML_ICON_POS3)
                    .append(newNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(newLine);
        } else {
            // EQUAL分支（默认）：对应原版 str11 + "<tr class=\"cps_tr2\"><td class=\"cp_frame cpi_td_w\">" + str2 + str5 + oldNum + str3 + oldLine + str4 + "<td width=\"5\"></td><td class=\"cp_frame cpi_td_w\">" + str2 + str5 + newNum + str3 + newLine
            compareResult.append(HTML_TR_SPACER_CLASS)
                    .append("<tr class=\"cps_tr2\"><td class=\"cp_frame cpi_td_w\">")
                    .append(HTML_TABLE_START)
                    .append(HTML_PLACEHOLDER_CELL)
                    .append(oldNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(oldLine)
                    .append(HTML_TABLE_END_MIDDLE)
                    .append(HTML_WIDTH_5_TD)
                    .append(HTML_INSERT_FRAME_START)
                    .append(HTML_TABLE_START)
                    .append(HTML_PLACEHOLDER_CELL)
                    .append(newNum)
                    .append(HTML_DIV_SEPARATOR)
                    .append(newLine);
        }

        // 添加统一的行结束标签（对应原版的 "</div></td></tr></table></td></tr>"）
        compareResult.append(HTML_ROW_END);
    }

    /**
     * 生成基于文件名的自定义Patch（使用新版本API重写）
     * 保持与原版完全相同的逻辑
     */
    private Patch<String> generateCustomFileNamePatch(List<String> original, List<String> revised) {
        Patch<String> patch = new Patch<String>();
        List<String> originalCopy = new ArrayList<String>(original);
        List<String> revisedCopy = new ArrayList<String>(revised);

        // 1. 处理文件名匹配的行（修改）
        for (int i = 0; i < originalCopy.size(); i++) {
            String origLine = originalCopy.get(i);
            String origName = extractFilename(origLine);
            if (origName == null) {
                continue;
            }

            for (int j = 0; j < revisedCopy.size(); j++) {
                String revLine = revisedCopy.get(j);
                String revName = extractFilename(revLine);
                if (revName != null && revName.equals(origName)) {
                    if (!origLine.equals(revLine)) {
                        List<String> origLines = new ArrayList<String>();
                        origLines.add(origLine);
                        Chunk<String> origChunk = new Chunk<String>(i, origLines);

                        List<String> revLines = new ArrayList<String>();
                        revLines.add(revLine);
                        Chunk<String> revChunk = new Chunk<String>(j, revLines);

                        // 使用新版本API创建ChangeDelta
                        // 注意：revChunk包含了目标文本内容
                        patch.addDelta(new ChangeDelta<String>(origChunk, revChunk));
                    }
                    revisedCopy.remove(j);
                    originalCopy.set(i, null);
                    break;
                }
            }
        }

        // 2. 处理剩余原始行（删除）
        for (int i = 0; i < originalCopy.size(); i++) {
            String origLine = originalCopy.get(i);
            if (origLine != null) {
                List<String> origLines = new ArrayList<String>();
                origLines.add(origLine);
                Chunk<String> origChunk = new Chunk<String>(i, origLines);
                Chunk<String> revChunk = new Chunk<String>(i, new ArrayList<String>());

                // 使用新版本API创建DeleteDelta
                patch.addDelta(new DeleteDelta<String>(origChunk, revChunk));
            }
        }

        // 3. 处理剩余修订行（新增）
        for (int j = 0; j < revisedCopy.size(); j++) {
            String revLine = revisedCopy.get(j);
            // 插入位置固定为原始列表的尾部（original.size()是合法的插入位置）
            int insertPos = original.size();
            Chunk<String> origChunk = new Chunk<String>(insertPos, new ArrayList<String>());
            List<String> revLines = new ArrayList<String>();
            revLines.add(revLine);
            Chunk<String> revChunk = new Chunk<String>(insertPos, revLines);

            // 使用新版本API创建InsertDelta
            patch.addDelta(new InsertDelta<String>(origChunk, revChunk));
        }

        return patch;
    }

    /**
     * 提取文件名方法（与原版保持一致）
     */
    private String extractFilename(String line) {
        if (line == null) {
            return null;
        }
        Matcher matcher = FILENAME_PATTERN.matcher(line);
        return matcher.find() ? matcher.group(1).trim() : null;
    }

    /**
     * 将DiffRow.Tag转换为与原版本一致的字符串
     * 确保HTML输出格式完全一致
     */
    private String convertTagToString(DiffRow.Tag tag) {
        switch (tag) {
            case EQUAL:
                return RESULTEQUAL;
            case CHANGE:
                return RESULTCHANGE;
            case INSERT:
                return RESULTINSERT;
            case DELETE:
                return RESULTDELETE;
            default:
                // 兜底方案
                return tag.toString();
        }
    }

    /**
     * 字符串替换方法（与原版保持一致）
     */
    private String getReplace(String str) {
        String res = str;
        if (str.contains("<br>")) {
            res = str.replace("<br>", "");
        }
        return res;
    }

    /**
     * 优化版本的文件读取方法（与原版保持一致）
     * @param filePath 文件路径
     * @return 文件内容字符串
     * @throws IOException 当读取文件发生错误时抛出
     */
    public static String readFileToString(String filePath) throws IOException {
        // 使用BufferedReader处理大文件，优化内存使用
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8))) {
            String line;
            boolean firstLine = true;
            while ((line = reader.readLine()) != null) {
                if (!firstLine) {
                    // 保留换行符
                    content.append('\n');
                }
                content.append(line);
                firstLine = false;
            }
        } catch (FileNotFoundException e) {
            throw new IOException("文件未找到: " + filePath, e);
        } catch (IOException e) {
            throw new IOException("读取文件失败: " + filePath, e);
        }
        return content.toString();
    }

    /**
     * 测试主方法（与原版保持一致）
     */
    public static void main(String[] args) {
        // 测试文件路径（需要根据实际情况调整）
        String fileA = "D://a11.txt";
        String fileB = "D://b11.txt";
        try {
            long start = System.currentTimeMillis();

            String aa = "\n\n###########################\n aa=1\n#######################\n\n# entegor.type:ieai cm\n# entegor.type=ieai\nentegor.type=ieai";
            String bb = "\n\n##################################################\n\n# entegor.type:ieai cm\n# entegor.type=ieai\nentegor.type=ieai";

            Map map =  new CompareSourceTargetContentManagerV2().compare(aa, bb);
            String v2Out = JSON.toJSONString(map);
            System.out.println(JSON.toJSONString(map));

            long end = System.currentTimeMillis();
            long duration = end - start;
            System.out.println("V2版本测试耗时: " + duration + " 毫秒");

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}

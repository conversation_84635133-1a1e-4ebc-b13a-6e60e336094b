package com.ideal.envc.common;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Collection;

/**
 * 文件读写操作工具类
 * 提供文件的创建、读取、更新、删除等基础操作功能
 *
 * <AUTHOR>
 */
public class FileOperationUtils {

    private static final Logger logger = LoggerFactory.getLogger(FileOperationUtils.class);

    /**
     * 文件系统目录分隔符
     */
    private static final String FILE_SEPARATOR = File.separator;

    /**
     * 时间戳格式
     */
    private static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmssSSS";

    /**
     * JSON文件扩展名
     */
    private static final String JSON_EXTENSION = ".json";

    /**
     * 校验文件路径参数
     *
     * @param filePath 文件路径
     * @param operation 操作名称（用于日志记录）
     * @return true-校验通过，false-校验失败
     */
    private static boolean validateFilePath(String filePath, String operation) {
        if (StringUtils.isBlank(filePath)) {
            logger.error("{}操作失败：文件路径不能为空", operation);
            return false;
        }
        return true;
    }

    /**
     * 校验并处理文件内容
     *
     * @param content 文件内容
     * @param operation 操作名称（用于日志记录）
     * @return 处理后的内容（null转为空字符串）
     */
    private static String validateAndProcessContent(String content, String operation) {
        if (content == null) {
            logger.warn("{}操作：文件内容为null，将使用空字符串", operation);
            return "";
        }
        return content;
    }

    /**
     * 创建父目录（如果不存在）
     *
     * @param path 文件路径
     * @return true-创建成功或已存在，false-创建失败
     */
    private static boolean ensureParentDirectory(Path path) {
        try {
            Path parentDir = path.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                logger.info("创建父目录成功：{}", parentDir);
            }
            return true;
        } catch (Exception e) {
            logger.error("创建父目录失败：{}", path.getParent(), e);
            return false;
        }
    }

    /**
     * 检查文件存在性和类型
     *
     * @param path 文件路径
     * @param operation 操作名称（用于日志记录）
     * @return true-文件存在且为普通文件，false-文件不存在或不是普通文件
     */
    private static boolean validateFileExists(Path path, String operation) {
        if (!Files.exists(path)) {
            logger.warn("{}操作失败：文件不存在 - {}", operation, path);
            return false;
        }

        if (!Files.isRegularFile(path)) {
            logger.error("{}操作失败：路径不是文件 - {}", operation, path);
            return false;
        }

        return true;
    }

    /**
     * 记录操作成功日志
     *
     * @param operation 操作名称
     * @param filePath 文件路径
     * @param additionalInfo 附加信息
     */
    private static void logOperationSuccess(String operation, String filePath, String additionalInfo) {
        if (StringUtils.isNotBlank(additionalInfo)) {
            logger.info("{}操作成功，路径：{}，{}", operation, filePath, additionalInfo);
        } else {
            logger.info("{}操作成功，路径：{}", operation, filePath);
        }
    }

    /**
     * 记录操作失败日志
     *
     * @param operation 操作名称
     * @param filePath 文件路径
     * @param exception 异常信息
     */
    private static void logOperationFailure(String operation, String filePath, Exception exception) {
        if (exception instanceof IOException) {
            logger.error("{}操作失败，路径：{}", operation, filePath, exception);
        } else {
            logger.error("{}操作异常，路径：{}", operation, filePath, exception);
        }
    }

    /**
     * 构建文件路径
     * 根据基础路径、子目录和文件名构建完整的文件路径
     *
     * @param basePath 基础路径
     * @param subDirectory 子目录（可选）
     * @param fileName 文件名
     * @return 构建后的完整文件路径
     */
    private static String buildFilePath(String basePath, String subDirectory, String fileName) {
        StringBuilder pathBuilder = new StringBuilder();
        pathBuilder.append(basePath);

        /* 确保基础路径以目录分隔符结尾 */
        if (!basePath.endsWith(FILE_SEPARATOR)) {
            pathBuilder.append(FILE_SEPARATOR);
        }

        /* 添加子目录（如果提供） */
        if (StringUtils.isNotBlank(subDirectory)) {
            pathBuilder.append(subDirectory);
            pathBuilder.append(FILE_SEPARATOR);
        }

        /* 添加文件名 */
        if (StringUtils.isNotBlank(fileName)) {
            pathBuilder.append(fileName);
        }

        return pathBuilder.toString();
    }

    /**
     * 生成文件全路径
     * 规则：基础路径 + 目录符 + 流程ID + 目录符 + 流程ID_时间戳.json
     *
     * @param basePath 基础路径
     * @param flowId   流程ID
     * @return 文件绝对路径（含文件名）
     */
    public static String generateFilePath(String basePath, String flowId) {
        logger.debug("开始生成文件路径，基础路径：{}，流程ID：{}", basePath, flowId);

        if (StringUtils.isBlank(basePath)) {
            logger.error("基础路径不能为空");
            throw new IllegalArgumentException("基础路径不能为空");
        }

        if (StringUtils.isBlank(flowId)) {
            logger.error("流程ID不能为空");
            throw new IllegalArgumentException("流程ID不能为空");
        }

        try {

            /* 构建文件名：流程ID_时间戳.json */
            String fileName = flowId  + JSON_EXTENSION;

            /* 使用公共方法构建文件路径 */
            String filePath = buildFilePath(basePath, flowId, fileName);

            logOperationSuccess("文件路径生成", filePath, null);

            return filePath;

        } catch (Exception e) {
            logger.error("生成文件路径失败，基础路径：{}，流程ID：{}", basePath, flowId, e);
            throw new RuntimeException("生成文件路径失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建文件并写入内容
     * 使用UTF-8编码写入文件
     *
     * @param content  文件内容
     * @param filePath 文件绝对路径（含文件名）
     * @return true-写入成功，false-写入失败
     */
    public static boolean createFile(String content, String filePath) {
        logger.debug("开始创建文件，文件路径：{}", filePath);

        if (!validateFilePath(filePath, "创建文件")) {
            return false;
        }

        content = validateAndProcessContent(content, "创建文件");

        try {
            Path path = Paths.get(filePath);

            /* 创建父目录（如果不存在） */
            if (!ensureParentDirectory(path)) {
                return false;
            }

            /* 写入文件内容 */
            Files.write(path, content.getBytes(StandardCharsets.UTF_8),
                       StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

            logOperationSuccess("创建文件", filePath, "内容长度：" + content.length());
            return true;

        } catch (Exception e) {
            logOperationFailure("创建文件", filePath, e);
            return false;
        }
    }

    /**
     * 读取文件内容
     * 使用UTF-8编码读取文件
     *
     * @param filePath 文件绝对路径（含文件名）
     * @return 文件内容字符串，读取失败返回null
     */
    public static String readFile(String filePath) {
        logger.debug("开始读取文件，文件路径：{}", filePath);

        if (!validateFilePath(filePath, "读取文件")) {
            return null;
        }

        try {
            Path path = Paths.get(filePath);

            /* 检查文件是否存在且为普通文件 */
            if (!validateFileExists(path, "读取文件")) {
                return null;
            }

            /* 读取文件内容 */
            byte[] bytes = Files.readAllBytes(path);
            String content = new String(bytes, StandardCharsets.UTF_8);

            logOperationSuccess("读取文件", filePath, "内容长度：" + content.length());
            return content;

        } catch (Exception e) {
            logOperationFailure("读取文件", filePath, e);
            return null;
        }
    }

    /**
     * 更新文件内容
     * 使用UTF-8编码写入文件，支持追加或替换模式
     *
     * @param content     文件内容
     * @param filePath    文件绝对路径（含文件名）
     * @param isReplace   是否替换原有内容，true-替换，false-追加，默认为true
     * @return true-更新成功，false-更新失败
     */
    public static boolean updateFile(String content, String filePath, boolean isReplace) {
        String operation = isReplace ? "替换更新文件" : "追加更新文件";
        logger.debug("开始{}，文件路径：{}", operation, filePath);

        if (!validateFilePath(filePath, operation)) {
            return false;
        }

        content = validateAndProcessContent(content, operation);

        try {
            Path path = Paths.get(filePath);

            /* 创建父目录（如果不存在） */
            if (!ensureParentDirectory(path)) {
                return false;
            }

            /* 根据模式选择写入方式 */
            if (isReplace) {
                /* 替换模式：覆盖原有内容 */
                Files.write(path, content.getBytes(StandardCharsets.UTF_8),
                           StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            } else {
                /* 追加模式：在原有内容后追加 */
                Files.write(path, content.getBytes(StandardCharsets.UTF_8),
                           StandardOpenOption.CREATE, StandardOpenOption.APPEND);
            }

            String modeInfo = isReplace ? "内容长度：" + content.length() : "追加内容长度：" + content.length();
            logOperationSuccess(operation, filePath, modeInfo);
            return true;

        } catch (Exception e) {
            logOperationFailure(operation, filePath, e);
            return false;
        }
    }

    /**
     * 更新文件内容（默认替换模式）
     * 使用UTF-8编码写入文件，默认替换原有内容
     *
     * @param content  文件内容
     * @param filePath 文件绝对路径（含文件名）
     * @return true-更新成功，false-更新失败
     */
    public static boolean updateFile(String content, String filePath) {
        return updateFile(content, filePath, true);
    }

    /**
     * 删除文件
     *
     * @param filePath 文件绝对路径（含文件名）
     * @return true-删除成功，false-删除失败
     */
    public static boolean deleteFile(String filePath) {
        logger.debug("开始删除文件，文件路径：{}", filePath);

        if (!validateFilePath(filePath, "删除文件")) {
            return false;
        }

        try {
            Path path = Paths.get(filePath);

            /* 检查文件是否存在 */
            if (!Files.exists(path)) {
                logger.warn("删除文件操作：文件不存在，无需删除 - {}", filePath);
                return true;
            }

            /* 检查是否为文件 */
            if (!Files.isRegularFile(path)) {
                logger.error("删除文件操作失败：路径不是文件 - {}", filePath);
                return false;
            }

            /* 删除文件 */
            Files.delete(path);
            logOperationSuccess("删除文件", filePath, null);
            return true;

        } catch (Exception e) {
            logOperationFailure("删除文件", filePath, e);
            return false;
        }
    }

    /**
     * 批量删除文件
     *
     * @param filePaths 文件绝对路径集合
     * @return true-全部删除成功，false-存在删除失败的文件
     */
    public static boolean batchDeleteFiles(Collection<String> filePaths) {
        logger.debug("开始批量删除文件，文件数量：{}", filePaths != null ? filePaths.size() : 0);

        if (filePaths == null || filePaths.isEmpty()) {
            logger.warn("批量删除文件操作：文件路径集合为空，无需删除");
            return true;
        }

        boolean allSuccess = true;
        int successCount = 0;
        int failCount = 0;

        for (String filePath : filePaths) {
            if (deleteFile(filePath)) {
                successCount++;
            } else {
                failCount++;
                allSuccess = false;
            }
        }

        String resultInfo = String.format("总数：%d，成功：%d，失败：%d",
                                         filePaths.size(), successCount, failCount);
        logOperationSuccess("批量删除文件", "多个文件", resultInfo);

        return allSuccess;
    }
}

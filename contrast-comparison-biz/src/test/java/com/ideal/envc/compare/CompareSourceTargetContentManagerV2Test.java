package com.ideal.envc.compare;

import com.github.difflib.text.DiffRow;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CompareSourceTargetContentManagerV2单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CompareSourceTargetContentManagerV2单元测试")
class CompareSourceTargetContentManagerV2Test {

    private CompareSourceTargetContentManagerV2 manager;

    @BeforeEach
    void setUp() {
        manager = CompareSourceTargetContentManagerV2.getInstance();
    }

    @Test
    @DisplayName("测试单例模式")
    void testGetInstance() {
        CompareSourceTargetContentManagerV2 instance1 = CompareSourceTargetContentManagerV2.getInstance();
        CompareSourceTargetContentManagerV2 instance2 = CompareSourceTargetContentManagerV2.getInstance();
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "应该返回同一个实例");
    }

    @Test
    @DisplayName("测试字符串比对 - 相同内容")
    void testCompareString_SameContent() {
        String sourceContent = "line1\nline2\nline3";
        String targetContent = "line1\nline2\nline3";
        
        Map<String, Object> result = manager.compare(sourceContent, targetContent);
        
        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET), "相同内容应该返回true");
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试字符串比对 - 不同内容")
    void testCompareString_DifferentContent() {
        String sourceContent = "line1\nline2\nline3";
        String targetContent = "line1\nmodified line2\nline3";
        
        Map<String, Object> result = manager.compare(sourceContent, targetContent);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET), "不同内容应该返回false");
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
        assertTrue(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT).toString().contains("comparison_space"));
    }

    @ParameterizedTest
    @MethodSource("nullStringTestCases")
    @DisplayName("测试字符串比对 - null值处理")
    void testCompareString_NullHandling(String sourceContent, String targetContent, boolean expectedRet) {
        Map<String, Object> result = manager.compare(sourceContent, targetContent);
        
        assertNotNull(result);
        assertEquals(expectedRet, result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    static Stream<Object[]> nullStringTestCases() {
        return Stream.of(
            new Object[]{null, null, true},
            new Object[]{null, "", true},
            new Object[]{"", null, true},
            new Object[]{null, "content", false},
            new Object[]{"content", null, false}
        );
    }

    @Test
    @DisplayName("测试列表比对 - 相同内容")
    void testCompareList_SameContent() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试列表比对 - 插入行")
    void testCompareList_InsertLines() {
        List<String> aLines = Arrays.asList("line1", "line3");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试列表比对 - 删除行")
    void testCompareList_DeleteLines() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试列表比对 - 修改行")
    void testCompareList_ChangeLines() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "modified line2", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试行数限制 - 源文件超过10000行")
    void testCompareList_SourceExceedsLimit() {
        List<String> aLines = new ArrayList<>();
        for (int i = 0; i < 10001; i++) {
            aLines.add("line" + i);
        }
        List<String> bLines = Arrays.asList("line1", "line2");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertEquals("The number of lines in the source file exceeds 10,000!!!", 
                    result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试行数限制 - 目标文件超过10000行")
    void testCompareList_TargetExceedsLimit() {
        List<String> aLines = Arrays.asList("line1", "line2");
        List<String> bLines = new ArrayList<>();
        for (int i = 0; i < 10001; i++) {
            bLines.add("line" + i);
        }
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertEquals("The number of lines in the target file exceeds 10,000!!!", 
                    result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试空列表比对")
    void testCompareList_EmptyLists() {
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试格式验证 - 有效格式")
    void testValidFormat() {
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123def456)",
            "file2.txt (size: 2.5 KB, permissions: -rwxr-xr-x, MD5: def456ghi789)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123def456)",
            "file2.txt (size: 2.6 KB, permissions: -rwxr-xr-x, MD5: def456ghi789)"
        );
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试格式验证 - 无效格式")
    void testInvalidFormat() {
        List<String> aLines = Arrays.asList("invalid format line1", "invalid format line2");
        List<String> bLines = Arrays.asList("invalid format line1", "modified invalid format line2");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        CompareSourceTargetContentManagerV2.RESULTDELETE,
        CompareSourceTargetContentManagerV2.RESULTINSERT,
        CompareSourceTargetContentManagerV2.RESULTCHANGE,
        CompareSourceTargetContentManagerV2.RESULTEQUAL
    })
    @DisplayName("测试HTML结果生成 - 不同标签类型")
    void testInitResultStr_DifferentTags(String tag) {
        StringBuilder compareResult = new StringBuilder();
        String oldNum = "1";
        String oldLine = "old line content";
        String newNum = "1";
        String newLine = "new line content";
        
        manager.initResultStr(tag, compareResult, oldNum, oldLine, newNum, newLine);
        
        assertNotNull(compareResult.toString());
        assertTrue(compareResult.length() > 0);
        
        // 验证包含预期的HTML结构
        String result = compareResult.toString();
        assertTrue(result.contains("<tr"));
        assertTrue(result.contains("</tr>"));
        assertTrue(result.contains("<td"));
        assertTrue(result.contains("</td>"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - DELETE标签")
    void testInitResultStr_DeleteTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTDELETE, 
                            compareResult, "1", "deleted line", "", "");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cp_frame abnormal cpi_td_w"));
        assertTrue(result.contains("icon_pos"));
        assertTrue(result.contains("deleted line"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - INSERT标签")
    void testInitResultStr_InsertTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTINSERT, 
                            compareResult, "", "", "1", "inserted line");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cp_frame complete cpi_td_w"));
        assertTrue(result.contains("icon_pos2"));
        assertTrue(result.contains("inserted line"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - CHANGE标签")
    void testInitResultStr_ChangeTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTCHANGE, 
                            compareResult, "1", "old line", "1", "new line");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cp_frame warning cpi_td_w"));
        assertTrue(result.contains("icon_pos3"));
        assertTrue(result.contains("old line"));
        assertTrue(result.contains("new line"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - EQUAL标签")
    void testInitResultStr_EqualTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTEQUAL, 
                            compareResult, "1", "same line", "1", "same line");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cps_tr2"));
        assertTrue(result.contains("same line"));
    }

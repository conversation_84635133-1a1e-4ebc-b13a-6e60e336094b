package com.ideal.envc.compare;

import com.github.difflib.text.DiffRow;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CompareSourceTargetContentManagerV2单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CompareSourceTargetContentManagerV2单元测试")
class CompareSourceTargetContentManagerV2Test {

    private CompareSourceTargetContentManagerV2 manager;

    @BeforeEach
    void setUp() {
        manager = CompareSourceTargetContentManagerV2.getInstance();
    }

    @Test
    @DisplayName("测试单例模式")
    void testGetInstance() {
        CompareSourceTargetContentManagerV2 instance1 = CompareSourceTargetContentManagerV2.getInstance();
        CompareSourceTargetContentManagerV2 instance2 = CompareSourceTargetContentManagerV2.getInstance();
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "应该返回同一个实例");
    }

    @Test
    @DisplayName("测试字符串比对 - 相同内容")
    void testCompareString_SameContent() {
        String sourceContent = "line1\nline2\nline3";
        String targetContent = "line1\nline2\nline3";
        
        Map<String, Object> result = manager.compare(sourceContent, targetContent);
        
        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET), "相同内容应该返回true");
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试字符串比对 - 不同内容")
    void testCompareString_DifferentContent() {
        String sourceContent = "line1\nline2\nline3";
        String targetContent = "line1\nmodified line2\nline3";
        
        Map<String, Object> result = manager.compare(sourceContent, targetContent);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET), "不同内容应该返回false");
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
        assertTrue(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT).toString().contains("comparison_space"));
    }

    @ParameterizedTest
    @MethodSource("nullStringTestCases")
    @DisplayName("测试字符串比对 - null值处理")
    void testCompareString_NullHandling(String sourceContent, String targetContent, boolean expectedRet) {
        Map<String, Object> result = manager.compare(sourceContent, targetContent);
        
        assertNotNull(result);
        assertEquals(expectedRet, result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    static Stream<Object[]> nullStringTestCases() {
        return Stream.of(
            new Object[]{null, null, true},
            new Object[]{null, "", true},
            new Object[]{"", null, true},
            new Object[]{null, "content", false},
            new Object[]{"content", null, false}
        );
    }

    @Test
    @DisplayName("测试列表比对 - 相同内容")
    void testCompareList_SameContent() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试列表比对 - 插入行")
    void testCompareList_InsertLines() {
        List<String> aLines = Arrays.asList("line1", "line3");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试列表比对 - 删除行")
    void testCompareList_DeleteLines() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试列表比对 - 修改行")
    void testCompareList_ChangeLines() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "modified line2", "line3");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试行数限制 - 源文件超过10000行")
    void testCompareList_SourceExceedsLimit() {
        List<String> aLines = new ArrayList<>();
        for (int i = 0; i < 10001; i++) {
            aLines.add("line" + i);
        }
        List<String> bLines = Arrays.asList("line1", "line2");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertEquals("The number of lines in the source file exceeds 10,000!!!", 
                    result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试行数限制 - 目标文件超过10000行")
    void testCompareList_TargetExceedsLimit() {
        List<String> aLines = Arrays.asList("line1", "line2");
        List<String> bLines = new ArrayList<>();
        for (int i = 0; i < 10001; i++) {
            bLines.add("line" + i);
        }
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertEquals("The number of lines in the target file exceeds 10,000!!!", 
                    result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试空列表比对")
    void testCompareList_EmptyLists() {
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试格式验证 - 有效格式")
    void testValidFormat() {
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123def456)",
            "file2.txt (size: 2.5 KB, permissions: -rwxr-xr-x, MD5: def456ghi789)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123def456)",
            "file2.txt (size: 2.6 KB, permissions: -rwxr-xr-x, MD5: def456ghi789)"
        );
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试格式验证 - 无效格式")
    void testInvalidFormat() {
        List<String> aLines = Arrays.asList("invalid format line1", "invalid format line2");
        List<String> bLines = Arrays.asList("invalid format line1", "modified invalid format line2");
        
        Map<String, Object> result = manager.compare(aLines, bLines);
        
        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        CompareSourceTargetContentManagerV2.RESULTDELETE,
        CompareSourceTargetContentManagerV2.RESULTINSERT,
        CompareSourceTargetContentManagerV2.RESULTCHANGE,
        CompareSourceTargetContentManagerV2.RESULTEQUAL
    })
    @DisplayName("测试HTML结果生成 - 不同标签类型")
    void testInitResultStr_DifferentTags(String tag) {
        StringBuilder compareResult = new StringBuilder();
        String oldNum = "1";
        String oldLine = "old line content";
        String newNum = "1";
        String newLine = "new line content";
        
        manager.initResultStr(tag, compareResult, oldNum, oldLine, newNum, newLine);
        
        assertNotNull(compareResult.toString());
        assertTrue(compareResult.length() > 0);
        
        // 验证包含预期的HTML结构
        String result = compareResult.toString();
        assertTrue(result.contains("<tr"));
        assertTrue(result.contains("</tr>"));
        assertTrue(result.contains("<td"));
        assertTrue(result.contains("</td>"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - DELETE标签")
    void testInitResultStr_DeleteTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTDELETE, 
                            compareResult, "1", "deleted line", "", "");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cp_frame abnormal cpi_td_w"));
        assertTrue(result.contains("icon_pos"));
        assertTrue(result.contains("deleted line"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - INSERT标签")
    void testInitResultStr_InsertTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTINSERT, 
                            compareResult, "", "", "1", "inserted line");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cp_frame complete cpi_td_w"));
        assertTrue(result.contains("icon_pos2"));
        assertTrue(result.contains("inserted line"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - CHANGE标签")
    void testInitResultStr_ChangeTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTCHANGE, 
                            compareResult, "1", "old line", "1", "new line");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cp_frame warning cpi_td_w"));
        assertTrue(result.contains("icon_pos3"));
        assertTrue(result.contains("old line"));
        assertTrue(result.contains("new line"));
    }

    @Test
    @DisplayName("测试HTML结果生成 - EQUAL标签")
    void testInitResultStr_EqualTag() {
        StringBuilder compareResult = new StringBuilder();
        
        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTEQUAL, 
                            compareResult, "1", "same line", "1", "same line");
        
        String result = compareResult.toString();
        assertTrue(result.contains("cps_tr2"));
        assertTrue(result.contains("same line"));
    }

    @Test
    @DisplayName("测试文件读取 - 正常文件")
    void testReadFileToString_NormalFile() throws Exception {
        // 创建临时文件
        java.io.File tempFile = java.io.File.createTempFile("test", ".txt");
        tempFile.deleteOnExit();

        String testContent = "line1\nline2\nline3";
        try (java.io.FileWriter writer = new java.io.FileWriter(tempFile)) {
            writer.write(testContent);
        }

        String result = CompareSourceTargetContentManagerV2.readFileToString(tempFile.getAbsolutePath());

        assertEquals(testContent, result);
    }

    @Test
    @DisplayName("测试文件读取 - 文件不存在")
    void testReadFileToString_FileNotFound() {
        String nonExistentFile = "non_existent_file.txt";

        IOException exception = assertThrows(IOException.class, () -> {
            CompareSourceTargetContentManagerV2.readFileToString(nonExistentFile);
        });

        assertTrue(exception.getMessage().contains("文件未找到"));
    }

    @Test
    @DisplayName("测试文件读取 - 空文件")
    void testReadFileToString_EmptyFile() throws Exception {
        // 创建空的临时文件
        java.io.File tempFile = java.io.File.createTempFile("empty", ".txt");
        tempFile.deleteOnExit();

        String result = CompareSourceTargetContentManagerV2.readFileToString(tempFile.getAbsolutePath());

        assertEquals("", result);
    }

    @Test
    @DisplayName("测试字符串分割 - 不同换行符")
    void testSplitToLines_DifferentLineBreaks() throws Exception {
        // 使用反射测试私有方法
        Method splitToLinesMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("splitToLines", String.class);
        splitToLinesMethod.setAccessible(true);

        // 测试Windows换行符
        @SuppressWarnings("unchecked")
        List<String> windowsResult = (List<String>) splitToLinesMethod.invoke(manager, "line1\r\nline2\r\nline3");
        assertEquals(Arrays.asList("line1", "line2", "line3"), windowsResult);

        // 测试Unix换行符
        @SuppressWarnings("unchecked")
        List<String> unixResult = (List<String>) splitToLinesMethod.invoke(manager, "line1\nline2\nline3");
        assertEquals(Arrays.asList("line1", "line2", "line3"), unixResult);

        // 测试Mac换行符
        @SuppressWarnings("unchecked")
        List<String> macResult = (List<String>) splitToLinesMethod.invoke(manager, "line1\rline2\rline3");
        assertEquals(Arrays.asList("line1", "line2", "line3"), macResult);
    }

    @Test
    @DisplayName("测试字符串分割 - 以换行符结尾")
    void testSplitToLines_EndingWithNewline() throws Exception {
        Method splitToLinesMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("splitToLines", String.class);
        splitToLinesMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) splitToLinesMethod.invoke(manager, "line1\nline2\n");
        assertEquals(Arrays.asList("line1", "line2", ""), result);
    }

    @Test
    @DisplayName("测试字符串分割 - null和空字符串")
    void testSplitToLines_NullAndEmpty() throws Exception {
        Method splitToLinesMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("splitToLines", String.class);
        splitToLinesMethod.setAccessible(true);

        // 测试null
        @SuppressWarnings("unchecked")
        List<String> nullResult = (List<String>) splitToLinesMethod.invoke(manager, (String) null);
        assertEquals(new ArrayList<>(), nullResult);

        // 测试空字符串
        @SuppressWarnings("unchecked")
        List<String> emptyResult = (List<String>) splitToLinesMethod.invoke(manager, "");
        assertEquals(new ArrayList<>(), emptyResult);
    }

    @Test
    @DisplayName("测试格式验证方法 - 有效格式")
    void testIsAllLinesValidFormat_ValidFormat() throws Exception {
        Method isAllLinesValidFormatMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("isAllLinesValidFormat", List.class);
        isAllLinesValidFormatMethod.setAccessible(true);

        List<String> validLines = Arrays.asList(
            "file1.txt (size: 1024 B)",
            "file2.txt (size: 2.5 KB, permissions: -rw-r--r--, MD5: abc123def456)",
            "file3.txt (size: 1.2 MB, permissions: -rwxr-xr-x, MD5: def456ghi789)"
        );

        Boolean result = (Boolean) isAllLinesValidFormatMethod.invoke(manager, validLines);
        assertTrue(result);
    }

    @Test
    @DisplayName("测试格式验证方法 - 无效格式")
    void testIsAllLinesValidFormat_InvalidFormat() throws Exception {
        Method isAllLinesValidFormatMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("isAllLinesValidFormat", List.class);
        isAllLinesValidFormatMethod.setAccessible(true);

        List<String> invalidLines = Arrays.asList(
            "file1.txt (size: 1024 B)",
            "invalid format line",
            "file3.txt (size: 1.2 MB)"
        );

        Boolean result = (Boolean) isAllLinesValidFormatMethod.invoke(manager, invalidLines);
        assertFalse(result);
    }

    @Test
    @DisplayName("测试格式验证方法 - null和空列表")
    void testIsAllLinesValidFormat_NullAndEmpty() throws Exception {
        Method isAllLinesValidFormatMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("isAllLinesValidFormat", List.class);
        isAllLinesValidFormatMethod.setAccessible(true);

        // 测试null
        Boolean nullResult = (Boolean) isAllLinesValidFormatMethod.invoke(manager, (List<String>) null);
        assertTrue(nullResult);

        // 测试空列表
        Boolean emptyResult = (Boolean) isAllLinesValidFormatMethod.invoke(manager, new ArrayList<String>());
        assertTrue(emptyResult);
    }

    @Test
    @DisplayName("测试文件名提取方法")
    void testExtractFilename() throws Exception {
        Method extractFilenameMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("extractFilename", String.class);
        extractFilenameMethod.setAccessible(true);

        // 测试有效文件名
        String result1 = (String) extractFilenameMethod.invoke(manager, "file1.txt (size: 1024 B)");
        assertEquals("file1.txt", result1);

        String result2 = (String) extractFilenameMethod.invoke(manager, "/path/to/file.log (size: 2.5 KB, permissions: -rw-r--r--)");
        assertEquals("/path/to/file.log", result2);

        // 测试无效格式
        String result3 = (String) extractFilenameMethod.invoke(manager, "invalid format");
        assertNull(result3);

        // 测试null
        String result4 = (String) extractFilenameMethod.invoke(manager, (String) null);
        assertNull(result4);
    }

    @Test
    @DisplayName("测试字符串替换方法")
    void testGetReplace() throws Exception {
        Method getReplaceMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("getReplace", String.class);
        getReplaceMethod.setAccessible(true);

        // 测试包含<br>的字符串
        String result1 = (String) getReplaceMethod.invoke(manager, "line1<br>line2<br>line3");
        assertEquals("line1line2line3", result1);

        // 测试不包含<br>的字符串
        String result2 = (String) getReplaceMethod.invoke(manager, "normal line");
        assertEquals("normal line", result2);

        // 测试空字符串
        String result3 = (String) getReplaceMethod.invoke(manager, "");
        assertEquals("", result3);
    }

    @Test
    @DisplayName("测试标签转换方法")
    void testConvertTagToString() throws Exception {
        Method convertTagToStringMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("convertTagToString", DiffRow.Tag.class);
        convertTagToStringMethod.setAccessible(true);

        // 测试所有标签类型
        String equalResult = (String) convertTagToStringMethod.invoke(manager, DiffRow.Tag.EQUAL);
        assertEquals(CompareSourceTargetContentManagerV2.RESULTEQUAL, equalResult);

        String changeResult = (String) convertTagToStringMethod.invoke(manager, DiffRow.Tag.CHANGE);
        assertEquals(CompareSourceTargetContentManagerV2.RESULTCHANGE, changeResult);

        String insertResult = (String) convertTagToStringMethod.invoke(manager, DiffRow.Tag.INSERT);
        assertEquals(CompareSourceTargetContentManagerV2.RESULTINSERT, insertResult);

        String deleteResult = (String) convertTagToStringMethod.invoke(manager, DiffRow.Tag.DELETE);
        assertEquals(CompareSourceTargetContentManagerV2.RESULTDELETE, deleteResult);
    }

    @Test
    @DisplayName("测试字符串到标签转换方法")
    void testConvertStringToTag() throws Exception {
        Method convertStringToTagMethod = CompareSourceTargetContentManagerV2.class.getDeclaredMethod("convertStringToTag", String.class);
        convertStringToTagMethod.setAccessible(true);

        // 测试所有字符串类型
        DiffRow.Tag equalResult = (DiffRow.Tag) convertStringToTagMethod.invoke(manager, CompareSourceTargetContentManagerV2.RESULTEQUAL);
        assertEquals(DiffRow.Tag.EQUAL, equalResult);

        DiffRow.Tag changeResult = (DiffRow.Tag) convertStringToTagMethod.invoke(manager, CompareSourceTargetContentManagerV2.RESULTCHANGE);
        assertEquals(DiffRow.Tag.CHANGE, changeResult);

        DiffRow.Tag insertResult = (DiffRow.Tag) convertStringToTagMethod.invoke(manager, CompareSourceTargetContentManagerV2.RESULTINSERT);
        assertEquals(DiffRow.Tag.INSERT, insertResult);

        DiffRow.Tag deleteResult = (DiffRow.Tag) convertStringToTagMethod.invoke(manager, CompareSourceTargetContentManagerV2.RESULTDELETE);
        assertEquals(DiffRow.Tag.DELETE, deleteResult);

        // 测试未知标签（兜底方案）
        DiffRow.Tag unknownResult = (DiffRow.Tag) convertStringToTagMethod.invoke(manager, "UNKNOWN");
        assertEquals(DiffRow.Tag.EQUAL, unknownResult);
    }

    @Test
    @DisplayName("测试复杂场景 - 混合操作")
    void testComplexScenario_MixedOperations() {
        List<String> aLines = Arrays.asList(
            "line1",
            "line2",
            "line3",
            "line4",
            "line5"
        );
        List<String> bLines = Arrays.asList(
            "line1",
            "modified line2",
            "line3",
            "new line",
            "line5",
            "additional line"
        );

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));

        String htmlResult = (String) result.get(CompareSourceTargetContentManagerV2.COMPARERESULT);
        assertNotNull(htmlResult);
        assertTrue(htmlResult.contains("comparison_space"));
        assertTrue(htmlResult.contains("Source"));
        assertTrue(htmlResult.contains("Target"));
    }

    @Test
    @DisplayName("测试自定义文件名Patch - 相同文件名不同内容")
    void testCustomFileNamePatch_SameFilenameDifferentContent() {
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123)",
            "file2.txt (size: 2048 B, permissions: -rw-r--r--, MD5: def456)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123)",
            "file2.txt (size: 2048 B, permissions: -rw-r--r--, MD5: xyz789)"
        );

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试自定义文件名Patch - 文件删除")
    void testCustomFileNamePatch_FileDeleted() {
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123)",
            "file2.txt (size: 2048 B, permissions: -rw-r--r--, MD5: def456)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123)"
        );

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试自定义文件名Patch - 文件新增")
    void testCustomFileNamePatch_FileAdded() {
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024 B, permissions: -rw-r--r--, MD5: abc123)",
            "file2.txt (size: 2048 B, permissions: -rw-r--r--, MD5: def456)"
        );

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试边界条件 - 单行比对")
    void testBoundaryCondition_SingleLine() {
        List<String> aLines = Arrays.asList("single line");
        List<String> bLines = Arrays.asList("single line");

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试边界条件 - 单行修改")
    void testBoundaryCondition_SingleLineModified() {
        List<String> aLines = Arrays.asList("original line");
        List<String> bLines = Arrays.asList("modified line");

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试HTML生成 - null值处理")
    void testInitResultStr_NullValues() {
        StringBuilder compareResult = new StringBuilder();

        // 测试null值不会导致异常
        assertDoesNotThrow(() -> {
            manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTEQUAL,
                                compareResult, null, null, null, null);
        });

        assertTrue(compareResult.length() > 0);
    }

    @Test
    @DisplayName("测试HTML生成 - 特殊字符")
    void testInitResultStr_SpecialCharacters() {
        StringBuilder compareResult = new StringBuilder();
        String specialChars = "<>&\"'";

        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTEQUAL,
                            compareResult, "1", specialChars, "1", specialChars);

        String result = compareResult.toString();
        assertTrue(result.contains(specialChars));
        assertTrue(result.length() > 0);
    }

    @Test
    @DisplayName("测试大数据量处理")
    void testLargeDataProcessing() {
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();

        // 创建5000行数据
        for (int i = 0; i < 5000; i++) {
            aLines.add("line" + i);
            bLines.add("line" + i);
        }

        // 修改中间的一行
        bLines.set(2500, "modified line2500");

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试性能 - 时间测量")
    void testPerformance_TimeMeasurement() {
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();

        // 创建1000行数据
        for (int i = 0; i < 1000; i++) {
            aLines.add("performance test line " + i);
            bLines.add("performance test line " + i);
        }

        long startTime = System.currentTimeMillis();
        Map<String, Object> result = manager.compare(aLines, bLines);
        long endTime = System.currentTimeMillis();

        assertNotNull(result);
        assertTrue((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));

        // 验证性能（应该在合理时间内完成）
        long duration = endTime - startTime;
        assertTrue(duration < 5000, "比对应该在5秒内完成，实际耗时: " + duration + "ms");
    }

    @Test
    @DisplayName("测试内存使用 - 大字符串处理")
    void testMemoryUsage_LargeStrings() {
        StringBuilder largeContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeContent.append("This is a very long line with lots of content to test memory usage ").append(i).append("\n");
        }

        String sourceContent = largeContent.toString();
        String targetContent = largeContent.toString().replace("500", "MODIFIED");

        Map<String, Object> result = manager.compare(sourceContent, targetContent);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试Unicode字符处理")
    void testUnicodeCharacterHandling() {
        String sourceContent = "中文测试\n日本語テスト\n한국어 테스트\n🚀 Emoji test";
        String targetContent = "中文测试\n日本語テスト修正\n한국어 테스트\n🚀 Emoji test";

        Map<String, Object> result = manager.compare(sourceContent, targetContent);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));

        String htmlResult = (String) result.get(CompareSourceTargetContentManagerV2.COMPARERESULT);
        assertTrue(htmlResult.contains("中文测试"));
        assertTrue(htmlResult.contains("日本語テスト"));
        assertTrue(htmlResult.contains("한국어"));
        assertTrue(htmlResult.contains("🚀"));
    }

    @Test
    @DisplayName("测试常量值验证")
    void testConstantValues() {
        assertEquals("EQUAL", CompareSourceTargetContentManagerV2.RESULTEQUAL);
        assertEquals("CHANGE", CompareSourceTargetContentManagerV2.RESULTCHANGE);
        assertEquals("INSERT", CompareSourceTargetContentManagerV2.RESULTINSERT);
        assertEquals("DELETE", CompareSourceTargetContentManagerV2.RESULTDELETE);
        assertEquals("compareResult", CompareSourceTargetContentManagerV2.COMPARERESULT);
        assertEquals("ret", CompareSourceTargetContentManagerV2.RET);
        assertEquals("sourceContent", CompareSourceTargetContentManagerV2.SOURCECONTENT);
        assertEquals("targetContent", CompareSourceTargetContentManagerV2.TARGETCONTENT);
        assertEquals("workFlowId", CompareSourceTargetContentManagerV2.WORKFLOWID);
        assertEquals("sourceActType", CompareSourceTargetContentManagerV2.SOURCETYPE);
        assertEquals("targetActType", CompareSourceTargetContentManagerV2.TARGETTYPE);
        assertEquals("CallFlow", CompareSourceTargetContentManagerV2.CALLFLOW);
    }

    @Test
    @DisplayName("测试文件读取 - IO异常处理")
    void testReadFileToString_IOExceptionHandling() throws Exception {
        // 创建临时文件
        java.io.File tempFile = java.io.File.createTempFile("test", ".txt");
        String filePath = tempFile.getAbsolutePath();

        // 写入内容
        try (java.io.FileWriter writer = new java.io.FileWriter(tempFile)) {
            writer.write("test content");
        }

        // 删除文件以模拟IO异常
        tempFile.delete();

        IOException exception = assertThrows(IOException.class, () -> {
            CompareSourceTargetContentManagerV2.readFileToString(filePath);
        });

        assertTrue(exception.getMessage().contains("文件未找到"));
    }

    @Test
    @DisplayName("测试文件读取 - 多行文件")
    void testReadFileToString_MultilineFile() throws Exception {
        java.io.File tempFile = java.io.File.createTempFile("multiline", ".txt");
        tempFile.deleteOnExit();

        String expectedContent = "line1\nline2\nline3\nline4";
        try (java.io.FileWriter writer = new java.io.FileWriter(tempFile)) {
            writer.write(expectedContent);
        }

        String result = CompareSourceTargetContentManagerV2.readFileToString(tempFile.getAbsolutePath());

        assertEquals(expectedContent, result);
    }

    @Test
    @DisplayName("测试极端情况 - 非常长的单行")
    void testExtremeCase_VeryLongSingleLine() {
        StringBuilder longLine = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            longLine.append("a");
        }

        List<String> aLines = Arrays.asList(longLine.toString());
        List<String> bLines = Arrays.asList(longLine.toString() + "b");

        Map<String, Object> result = manager.compare(aLines, bLines);

        assertNotNull(result);
        assertFalse((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
        assertNotNull(result.get(CompareSourceTargetContentManagerV2.COMPARERESULT));
    }

    @Test
    @DisplayName("测试HTML容量预估功能")
    void testHtmlCapacityEstimation() {
        StringBuilder compareResult = new StringBuilder();
        String longOldLine = "a".repeat(1000);
        String longNewLine = "b".repeat(1000);

        manager.initResultStr(CompareSourceTargetContentManagerV2.RESULTCHANGE,
                            compareResult, "1", longOldLine, "1", longNewLine);

        assertTrue(compareResult.length() > 2000);
        assertTrue(compareResult.toString().contains(longOldLine));
        assertTrue(compareResult.toString().contains(longNewLine));
    }

    @Test
    @DisplayName("测试线程安全性")
    void testThreadSafety() throws InterruptedException {
        final int threadCount = 10;
        final List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
        final List<Boolean> results = Collections.synchronizedList(new ArrayList<>());

        Thread[] threads = new Thread[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                try {
                    List<String> aLines = Arrays.asList("line1", "line2", "thread" + threadId);
                    List<String> bLines = Arrays.asList("line1", "line2", "thread" + threadId);

                    Map<String, Object> result = manager.compare(aLines, bLines);
                    results.add((Boolean) result.get(CompareSourceTargetContentManagerV2.RET));
                } catch (Exception e) {
                    exceptions.add(e);
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证结果
        assertTrue(exceptions.isEmpty(), "不应该有异常发生");
        assertEquals(threadCount, results.size());
        for (Boolean result : results) {
            assertTrue(result, "所有比对结果都应该为true");
        }
    }
}
